<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/ico/Planfuly_Logo_Mark.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    <title>Engage Planfuly</title>
    <script>
      (function() {
        try {
          var storageKey = 'planfuly-ui-theme';
          var stored = localStorage.getItem(storageKey);
          var media = window.matchMedia('(prefers-color-scheme: dark)');
          var theme = stored || 'system';
          var applied = theme === 'system' ? (media.matches ? 'dark' : 'light') : theme;
          var root = document.documentElement;
          root.classList.remove('light', 'dark');
          root.classList.add(applied);
          root.setAttribute('data-theme', applied);
          root.style.colorScheme = applied;
        } catch (e) {}
      })();
    </script>

    <script>
      // Suppress passive event listener warnings from third-party libraries
      (function() {
        if (typeof console !== 'undefined' && console.warn) {
          var originalWarn = console.warn;
          console.warn = function() {
            var message = arguments[0];
            if (typeof message === 'string' &&
                (message.includes('Added non-passive event listener') ||
                 message.includes('passive event listener'))) {
              return; // Suppress the warning
            }
            return originalWarn.apply(console, arguments);
          };
        }
      })();
    </script>

  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
