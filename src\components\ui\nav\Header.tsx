import { ModeToggle } from "@/components/mode-toggle"
import { LogoMark, TextMark } from "@/components/ui/basic/logo"
import { But<PERSON> } from "@/components/ui/basic/button"
import { auth } from "@/lib/firebase"
import { signOut } from "firebase/auth"
import { Link } from "react-router-dom"
// import { Heading4 } from "@/components/ui/typography"

interface HeaderProps {
  className?: string
}

export function Header({ className }: HeaderProps) {
  const handleLogout = async () => {
    try {
      await signOut(auth)
    } catch (e) {
      console.warn("Logout failed", e)
    }
  }

  return (
    <header className={`relative ${className || ""}`}>
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <ModeToggle />
        <Button size="sm" onClick={handleLogout}>Logout</Button>
      </div>

      <div className="flex items-end px-10 py-2 bg-secondary">
        <LogoMark height={50} />
        <div className="flex flex-col items-center px-2 leading-none">
          {/* <Heading4 className="italic leading-none mb-2 text-muted-foreground">Engage</Heading4> */}
          <Link to="/dashboard">
            <TextMark height={10} className="py-1 cursor-pointer" />
          </Link>
        </div>
      </div>
    </header>
  )
}
