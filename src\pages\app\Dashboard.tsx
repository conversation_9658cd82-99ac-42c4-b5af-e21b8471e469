import { Layout } from "@/components/ui/layout"
import { Button } from "@/components/ui/basic/button"
import { useNavigate } from "react-router-dom"
import { Home, Users, Plus, Settings } from "lucide-react"
import type { NavItem } from "@/components/ui/nav"

function Dashboard() {
  const navigate = useNavigate()

  const goToAdminMode = () => navigate("/clients")
  const goToPresentationMode = () => { }

  // Sample navigation items for demonstration
  const navItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Home size={20} />,
      active: true,
      onClick: () => navigate('/dashboard')
    },
    {
      id: 'clients',
      label: 'Clients',
      icon: <Users size={20} />,
      onClick: () => navigate('/clients')
    },
    {
      id: 'add-client',
      label: 'Add Client',
      icon: <Plus size={20} />,
      onClick: () => navigate('/clients/new')
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings size={20} />,
      disabled: true // Example of disabled item
    }
  ]

  const handleNavItemClick = (item: NavItem) => {
    console.log('Navigation item clicked:', item.label)
  }

  return (
    <Layout
      title=""
      navItems={navItems}
      onNavItemClick={handleNavItemClick}
    >
        <div className="flex-1 flex items-center justify-center gap-4">
          <Button size="lg" onClick={goToAdminMode}>Admin Mode</Button>
          <Button size="lg" variant="callToAction" onClick={goToPresentationMode}>Presentation Mode</Button>
        </div>
    </Layout>
  )
}

export default Dashboard
